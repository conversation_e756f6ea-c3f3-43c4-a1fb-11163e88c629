trigger: none

appendCommitMessageToRunName: false
name: $(date:yyyyMMdd)$(rev:.r) • Checkov Tests

variables:
  - group: TEST

jobs:
- job: Control
  timeoutInMinutes: 600  # 10 hours maximum
  pool:
    name: DEV-AksPool-centralagent-Deploy
  steps:
    - checkout: self
      fetchDepth: 0
    - task: AzureCLI@2
      displayName: Control checkov test pipelines
      env:
        AZURE_DEVOPS_EXT_PAT: $(System.AccessToken)
      inputs:
        azureSubscription: DEV-OTP-DD-COEINFDEV-sub-dev-01-FED
        scriptType: bash
        scriptLocation: inlineScript
        inlineScript: |
          az devops configure --defaults organization=https://dev.azure.com/ADOS-OTPHU-01
          az devops configure --defaults project=OTPHU-COE-TEMPLATESPEC

          config_file=$(Build.SourcesDirectory)/test-pipelines/config.json
          repos=$(jq -r '.repos[].name' $config_file)

          declare -A result_files
          declare -a pids

          result_dir="result"
          mkdir $result_dir
          max_parallel_jobs=10

          for repo in $repos; do
              [[ -z "$repo" ]] && continue
              echo "[INFO] Repo: $repo"
              echo "[INFO]   Pipeline: ${repo}_checkov"
          done

          function wait_for_jobs {
            sleep 30s
            job_wait_counter=0
            max_job_wait=240  # 2 hours (240 * 30s)
            while [ $(jobs -rp | wc -l) -ge $max_parallel_jobs ] && [ $job_wait_counter -lt $max_job_wait ]; do
              ((job_wait_counter++))
              sleep 30s
            done

            if [ $job_wait_counter -ge $max_job_wait ]; then
              echo "##vso[task.logissue type=warning]Job wait timeout reached. Some background jobs may be stuck. Active job PIDs: $(jobs -rp | tr '\n' ' ')"
            fi
          }

          function process_repo {
            local repo=$1
            
            wait_for_jobs

            full_output=$(az pipelines run --name "${repo}_checkov" --branch "refs/heads/main" --query "id" --output tsv 2>&1)
            if [[ $full_output =~ ^[0-9]+$ ]]; then
              build_id=$full_output
            else
              build_id=""
            fi

            key="${repo}#main#${repo}_checkov"
            result_file="$result_dir/$key.result"
            result_files[$key]=$result_file

            if [[ -z "$build_id" ]]; then
              echo "##vso[task.logissue type=warning]FAILED to trigger ${repo}_checkov pipeline for $repo. Output: $full_output"
              echo "failed_to_trigger;failed" > $result_file
              return
            fi

            echo "[INFO] Started $repo/main/${repo}_checkov: $build_id"

            status=""
            timeout_counter=0
            max_timeout=120  # 2 hours (120 * 60s)
            while [[ $status != "completed" ]] && [[ $timeout_counter -lt $max_timeout ]]; do
              sleep 60s
              ((timeout_counter++))
              status=$(az pipelines runs show --id "$build_id" --query "status" --output tsv || echo "failed")

              # Log every 30 minutes
              if [[ $((timeout_counter % 30)) -eq 0 ]]; then
                echo "[INFO] Still waiting for $repo/main/${repo}_checkov (build $build_id): $status ($timeout_counter/$max_timeout min)"
              fi

              # Break if status indicates completion or failure
              if [[ $status == "failed" ]] || [[ $status == "cancelled" ]]; then
                echo "[INFO] Pipeline $repo/main/${repo}_checkov ended with status: $status"
                break
              fi
            done

            # Check for timeout and get final result
            if [[ $timeout_counter -ge $max_timeout ]]; then
              status="timeout"
              result="timeout"
              echo "##vso[task.logissue type=warning]Pipeline $repo/main/${repo}_checkov timed out after $max_timeout minutes"
            else
              result=$(az pipelines runs show --id "$build_id" --query "result" --output tsv || echo "failed")
              if [[ $result != "succeeded" ]]; then
                echo "##vso[task.logissue type=warning]Pipeline $repo/main/${repo}_checkov completed with result: $result"
              else
                echo "[INFO] Pipeline $repo/main/${repo}_checkov completed successfully"
              fi
            fi
            echo "$build_id;$result" > $result_file
            echo "Completed processing repo: $repo"
          }

          pids=()
          declare -A pid_to_repo
          for repo in $repos; do
            wait_for_jobs
            echo "Launching process for repo: $repo"
            process_repo "$repo" &
            current_pid=$!
            pids+=($current_pid)
            pid_to_repo[$current_pid]=$repo
          done

          # Wait for all background processes
          wait_counter=0
          while [ ${#pids[@]} -gt 0 ]; do
            for i in "${!pids[@]}"; do
              pid="${pids[$i]}"
              repo_name="${pid_to_repo[$pid]}"
              
              if ! kill -0 "$pid" 2>/dev/null; then
                echo "[INFO] Background process for '$repo_name' has finished"
                unset 'pids[$i]'
                unset 'pid_to_repo[$pid]'
              else
                if [[ $wait_counter -gt 0 && $((wait_counter % 30)) -eq 0 ]]; then
                  echo "[INFO] Background process for '$repo_name' is still running..."
                fi
              fi
            done
            
            pids=("${pids[@]}")
            
            if [ ${#pids[@]} -gt 0 ]; then
              sleep 60
              ((wait_counter++))
            fi
          done
          
          echo "All background processes completed"

          # Collect all test results grouped by repo
          declare -A test_results_grouped
          for result_file in $result_dir/*.result; do
            key=$(basename "$result_file" .result)
            formatted_key=$(echo "$key" | tr '#' '/')

            content=$(cat "$result_file")
            build_id=$(echo "$content" | cut -d';' -f1)
            result=$(echo "$content" | cut -d';' -f2)

            repo=$(echo "$formatted_key" | cut -d'/' -f1)
            tag=$(echo "$formatted_key" | cut -d'/' -f2)
            pipeline=$(echo "$formatted_key" | cut -d'/' -f3)
            test_results_grouped["$repo"]+='{"tag":"'$tag'","pipeline":"'$pipeline'","result":"'$result'","buildId":"'$build_id'"},'
          done

          # Save all tests data for further processing (structured JSON)
          if [ ${#test_results_grouped[@]} -gt 0 ]; then
            json_output="{"
            first=true
            for repo in "${!test_results_grouped[@]}"; do
              if ! $first; then
                json_output+=","
              fi
              json_output+="\"$repo\":["
              json_output+="${test_results_grouped[$repo]%?}"  # Remove last comma
              json_output+="]"
              first=false
            done
            json_output+="}"
            echo "$json_output" | jq . > $(Pipeline.Workspace)/checkov_results.json
            echo "All checkov test results saved to checkov_results.json"
          fi
    - task: PublishPipelineArtifact@1
      displayName: Publish checkov_results.json artifact
      condition: always()
      inputs:
        targetPath: '$(Pipeline.Workspace)/checkov_results.json'
        artifact: 'checkov_results'
        publishLocation: 'pipeline'
